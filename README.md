# Linux等保基线核查工具

基于Go语言和Fyne框架开发的Linux等保基线核查工具，支持单主机和多主机批量核查，提供GUI界面和结果导出功能。

## 项目结构

```
linux-security-checker/
├── cmd/                          # 应用程序入口
│   └── linux-security-checker/   # 主程序
├── internal/                     # 内部包，不对外暴露
│   ├── app/                      # 应用程序主逻辑
│   ├── config/                   # 配置管理
│   ├── database/                 # 数据库操作
│   ├── host/                     # 主机管理
│   ├── check/                    # 核查引擎
│   ├── export/                   # 结果导出
│   └── ui/                       # GUI界面
├── pkg/                          # 可复用的公共包
│   ├── logger/                   # 日志管理
│   ├── ssh/                      # SSH连接
│   └── utils/                    # 工具函数
├── configs/                      # 配置文件
├── assets/                       # 静态资源
│   ├── icons/                    # 图标文件
│   └── templates/                # 模板文件
├── docs/                         # 文档
├── test/                         # 测试文件
├── go.mod                        # Go模块文件
├── go.sum                        # 依赖校验文件
└── README.md                     # 项目说明
```

## 技术栈

- **开发语言**: Go 1.21+
- **GUI框架**: Fyne v2.4+
- **数据库**: SQLite 3
- **SSH库**: golang.org/x/crypto/ssh
- **配置管理**: Viper
- **日志库**: logrus

## 功能特性

- 支持单主机和多主机批量核查
- 基于等保2.0标准的核查项目
- 直观的GUI界面
- 多格式结果导出（PDF、Excel、JSON/XML）
- 跨平台兼容性

## 开发阶段

1. **第一阶段**: 基础框架搭建（2周）
2. **第二阶段**: 主机管理功能（2周）
3. **第三阶段**: 核查引擎开发（3周）
4. **第四阶段**: 界面完善（2周）
5. **第五阶段**: 导出和优化（1周）

## 构建和运行

### Windows版本构建

```bash
# 使用Makefile构建
make windows

# 或使用构建脚本
./build-windows.sh

# 或手动构建
CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build -o bin/linux-security-checker.exe cmd/linux-security-checker/main.go
```

### 开发命令

```bash
# 安装依赖
go mod tidy

# 代码格式化
make fmt

# 代码检查
make vet

# 运行测试
make test

# 清理构建文件
make clean

# 查看所有可用命令
make help
```

## 许可证

本项目采用MIT许可证。
