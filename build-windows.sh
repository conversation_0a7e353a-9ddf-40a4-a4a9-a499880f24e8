#!/bin/bash

# Linux等保基线核查工具 - Windows构建脚本

echo "开始构建Windows版本..."

# 设置环境变量
export GOOS=windows
export GOARCH=amd64
export CGO_ENABLED=0

# 创建输出目录
mkdir -p bin

# 构建应用程序
echo "正在编译..."
go build -ldflags="-s -w" -o bin/linux-security-checker.exe cmd/linux-security-checker/main.go

if [ $? -eq 0 ]; then
    echo "✓ Windows版本构建成功！"
    echo "输出文件: bin/linux-security-checker.exe"
    ls -la bin/linux-security-checker.exe
    echo "文件大小: $(du -h bin/linux-security-checker.exe | cut -f1)"
else
    echo "✗ 构建失败！"
    exit 1
fi

echo "构建完成！"
