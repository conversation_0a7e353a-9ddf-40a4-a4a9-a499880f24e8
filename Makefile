# Linux等保基线核查工具 Makefile

# 变量定义
APP_NAME = linux-security-checker
VERSION = 1.0.0
BUILD_DIR = bin
MAIN_FILE = cmd/linux-security-checker/main.go

# 默认目标
.PHONY: all
all: windows

# Windows构建
.PHONY: windows
windows:
	@echo "构建Windows版本..."
	@mkdir -p $(BUILD_DIR)
	@CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build \
		-ldflags="-s -w -X main.version=$(VERSION)" \
		-o $(BUILD_DIR)/$(APP_NAME).exe $(MAIN_FILE)
	@echo "✓ Windows版本构建完成: $(BUILD_DIR)/$(APP_NAME).exe"

# 清理构建文件
.PHONY: clean
clean:
	@echo "清理构建文件..."
	@rm -rf $(BUILD_DIR)
	@echo "✓ 清理完成"

# 运行测试
.PHONY: test
test:
	@echo "运行测试..."
	@go test -v ./...

# 代码格式化
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	@go fmt ./...
	@echo "✓ 代码格式化完成"

# 代码检查
.PHONY: vet
vet:
	@echo "代码检查..."
	@go vet ./...
	@echo "✓ 代码检查完成"

# 依赖管理
.PHONY: tidy
tidy:
	@echo "整理依赖..."
	@go mod tidy
	@echo "✓ 依赖整理完成"

# 完整检查
.PHONY: check
check: fmt vet test
	@echo "✓ 所有检查完成"

# 显示帮助信息
.PHONY: help
help:
	@echo "可用的构建目标:"
	@echo "  windows  - 构建Windows版本"
	@echo "  clean    - 清理构建文件"
	@echo "  test     - 运行测试"
	@echo "  fmt      - 格式化代码"
	@echo "  vet      - 代码检查"
	@echo "  tidy     - 整理依赖"
	@echo "  check    - 运行所有检查"
	@echo "  help     - 显示此帮助信息"
