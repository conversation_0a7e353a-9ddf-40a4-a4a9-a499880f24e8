package database

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB 全局数据库实例
var DB *gorm.DB

// Config 数据库配置
type Config struct {
	Type           string
	Path           string
	MaxConnections int
	AutoMigrate    bool
	LogLevel       string
}

// Init 初始化数据库连接
func Init(config Config) error {
	var err error
	
	// 根据数据库类型选择驱动
	switch config.Type {
	case "sqlite":
		err = initSQLite(config)
	default:
		return fmt.Errorf("不支持的数据库类型: %s", config.Type)
	}

	if err != nil {
		return fmt.Errorf("初始化数据库失败: %w", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	sqlDB.SetMaxOpenConns(config.MaxConnections)
	sqlDB.SetMaxIdleConns(config.MaxConnections / 2)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 自动迁移
	if config.AutoMigrate {
		if err := autoMigrate(); err != nil {
			return fmt.Errorf("数据库迁移失败: %w", err)
		}
	}

	return nil
}

// initSQLite 初始化SQLite数据库
func initSQLite(config Config) error {
	// 确保数据库目录存在
	dbDir := filepath.Dir(config.Path)
	if err := ensureDir(dbDir); err != nil {
		return fmt.Errorf("创建数据库目录失败: %w", err)
	}

	// 设置日志级别
	logLevel := logger.Silent
	switch config.LogLevel {
	case "debug":
		logLevel = logger.Info
	case "info":
		logLevel = logger.Warn
	case "warn":
		logLevel = logger.Error
	}

	// 连接数据库
	var err error
	DB, err = gorm.Open(sqlite.Open(config.Path), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})

	return err
}

// autoMigrate 自动迁移数据库表结构
func autoMigrate() error {
	models := []interface{}{
		&Host{},
		&HostGroup{},
		&CheckTemplate{},
		&CheckTask{},
		&CheckResult{},
		&CheckItem{},
		&SystemInfo{},
	}

	for _, model := range models {
		if err := DB.AutoMigrate(model); err != nil {
			return fmt.Errorf("迁移模型 %T 失败: %w", model, err)
		}
	}

	// 初始化默认数据
	if err := initDefaultData(); err != nil {
		return fmt.Errorf("初始化默认数据失败: %w", err)
	}

	return nil
}

// initDefaultData 初始化默认数据
func initDefaultData() error {
	// 检查是否已有数据
	var count int64
	if err := DB.Model(&CheckTemplate{}).Count(&count).Error; err != nil {
		return err
	}

	// 如果已有模板数据，跳过初始化
	if count > 0 {
		return nil
	}

	// 创建默认核查模板
	defaultTemplates := []CheckTemplate{
		{
			Name:        "等保2.0基础核查",
			Description: "基于等保2.0标准的基础安全核查项目",
			Version:     "1.0",
			Category:    "security",
			Content:     `{"items": []}`, // 后续会添加具体的核查项
			IsBuiltIn:   true,
		},
		{
			Name:        "系统配置核查",
			Description: "Linux系统基础配置安全核查",
			Version:     "1.0",
			Category:    "system",
			Content:     `{"items": []}`,
			IsBuiltIn:   true,
		},
	}

	for _, template := range defaultTemplates {
		if err := DB.Create(&template).Error; err != nil {
			return fmt.Errorf("创建默认模板失败: %w", err)
		}
	}

	return nil
}

// ensureDir 确保目录存在
func ensureDir(dir string) error {
	if dir == "" || dir == "." {
		return nil
	}
	
	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			if os.IsNotExist(err) {
				return os.MkdirAll(dir, 0755)
			}
			return err
		}
		return nil
	})
}

// Close 关闭数据库连接
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// Transaction 执行事务
func Transaction(fn func(*gorm.DB) error) error {
	return DB.Transaction(fn)
}

// IsTableExists 检查表是否存在
func IsTableExists(tableName string) bool {
	return DB.Migrator().HasTable(tableName)
}

// DropTable 删除表
func DropTable(model interface{}) error {
	return DB.Migrator().DropTable(model)
}

// CreateTable 创建表
func CreateTable(model interface{}) error {
	return DB.Migrator().CreateTable(model)
}
