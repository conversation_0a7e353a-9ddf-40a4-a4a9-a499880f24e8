package database

import (
	"time"

	"gorm.io/gorm"
)

// Host 主机信息模型
type Host struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Name        string    `gorm:"size:100;not null" json:"name"`
	IP          string    `gorm:"size:45;not null;uniqueIndex" json:"ip"`
	Port        int       `gorm:"default:22" json:"port"`
	Username    string    `gorm:"size:50;not null" json:"username"`
	Password    string    `gorm:"size:255" json:"password,omitempty"`
	PrivateKey  string    `gorm:"type:text" json:"private_key,omitempty"`
	OS          string    `gorm:"size:50" json:"os"`
	Version     string    `gorm:"size:100" json:"version"`
	Status      string    `gorm:"size:20;default:'unknown'" json:"status"`
	Description string    `gorm:"size:500" json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	HostGroups []HostGroup `gorm:"many2many:host_group_members;" json:"host_groups,omitempty"`
	CheckTasks []CheckTask `gorm:"foreignKey:TargetID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"check_tasks,omitempty"`
}

// HostGroup 主机组模型
type HostGroup struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Name        string    `gorm:"size:100;not null;uniqueIndex" json:"name"`
	Description string    `gorm:"size:500" json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Hosts      []Host      `gorm:"many2many:host_group_members;" json:"hosts,omitempty"`
	CheckTasks []CheckTask `gorm:"foreignKey:TargetID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"check_tasks,omitempty"`
}

// CheckTemplate 核查模板模型
type CheckTemplate struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Name        string    `gorm:"size:100;not null;uniqueIndex" json:"name"`
	Description string    `gorm:"size:500" json:"description"`
	Version     string    `gorm:"size:20" json:"version"`
	Category    string    `gorm:"size:50" json:"category"`
	Content     string    `gorm:"type:text;not null" json:"content"`
	IsBuiltIn   bool      `gorm:"default:false" json:"is_built_in"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	CheckTasks []CheckTask `gorm:"foreignKey:TemplateID;constraint:OnUpdate:CASCADE,OnDelete:RESTRICT;" json:"check_tasks,omitempty"`
}

// CheckTask 核查任务模型
type CheckTask struct {
	ID         uint      `gorm:"primaryKey" json:"id"`
	Name       string    `gorm:"size:100;not null" json:"name"`
	TargetType string    `gorm:"size:20;not null" json:"target_type"` // host, group
	TargetID   uint      `gorm:"not null" json:"target_id"`
	TemplateID uint      `gorm:"not null" json:"template_id"`
	Status     string    `gorm:"size:20;default:'pending'" json:"status"` // pending, running, completed, failed
	Progress   float64   `gorm:"default:0" json:"progress"`
	StartTime  *time.Time `json:"start_time"`
	EndTime    *time.Time `json:"end_time"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Template    CheckTemplate `gorm:"foreignKey:TemplateID" json:"template,omitempty"`
	CheckResults []CheckResult `gorm:"foreignKey:TaskID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"check_results,omitempty"`
}

// CheckResult 核查结果模型
type CheckResult struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	TaskID      uint      `gorm:"not null;index" json:"task_id"`
	HostID      uint      `gorm:"not null;index" json:"host_id"`
	CheckItem   string    `gorm:"size:200;not null" json:"check_item"`
	Category    string    `gorm:"size:50" json:"category"`
	Level       string    `gorm:"size:20" json:"level"` // high, medium, low
	Status      string    `gorm:"size:20" json:"status"` // pass, fail, warning, error
	Expected    string    `gorm:"type:text" json:"expected"`
	Actual      string    `gorm:"type:text" json:"actual"`
	Command     string    `gorm:"type:text" json:"command"`
	Output      string    `gorm:"type:text" json:"output"`
	ErrorMsg    string    `gorm:"type:text" json:"error_msg"`
	Suggestion  string    `gorm:"type:text" json:"suggestion"`
	CreatedAt   time.Time `json:"created_at"`

	// 关联关系
	Task CheckTask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
	Host Host      `gorm:"foreignKey:HostID" json:"host,omitempty"`
}

// CheckItem 核查项模型
type CheckItem struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	Name        string `gorm:"size:200;not null" json:"name"`
	Category    string `gorm:"size:50;not null" json:"category"`
	Level       string `gorm:"size:20;not null" json:"level"`
	Description string `gorm:"size:500" json:"description"`
	Command     string `gorm:"type:text;not null" json:"command"`
	Expected    string `gorm:"type:text" json:"expected"`
	Suggestion  string `gorm:"type:text" json:"suggestion"`
	IsEnabled   bool   `gorm:"default:true" json:"is_enabled"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// SystemInfo 系统信息模型
type SystemInfo struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	HostID       uint      `gorm:"not null;uniqueIndex" json:"host_id"`
	Hostname     string    `gorm:"size:100" json:"hostname"`
	OS           string    `gorm:"size:50" json:"os"`
	OSVersion    string    `gorm:"size:100" json:"os_version"`
	Kernel       string    `gorm:"size:100" json:"kernel"`
	Architecture string    `gorm:"size:20" json:"architecture"`
	CPUInfo      string    `gorm:"type:text" json:"cpu_info"`
	MemoryInfo   string    `gorm:"type:text" json:"memory_info"`
	DiskInfo     string    `gorm:"type:text" json:"disk_info"`
	NetworkInfo  string    `gorm:"type:text" json:"network_info"`
	LastUpdated  time.Time `json:"last_updated"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	// 关联关系
	Host Host `gorm:"foreignKey:HostID" json:"host,omitempty"`
}

// TableName 方法用于指定表名
func (Host) TableName() string {
	return "hosts"
}

func (HostGroup) TableName() string {
	return "host_groups"
}

func (CheckTemplate) TableName() string {
	return "check_templates"
}

func (CheckTask) TableName() string {
	return "check_tasks"
}

func (CheckResult) TableName() string {
	return "check_results"
}

func (CheckItem) TableName() string {
	return "check_items"
}

func (SystemInfo) TableName() string {
	return "system_info"
}
