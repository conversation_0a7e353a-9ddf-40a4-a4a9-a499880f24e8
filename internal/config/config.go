package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config 应用程序配置结构
type Config struct {
	App      AppConfig      `mapstructure:"app"`
	Database DatabaseConfig `mapstructure:"database"`
	SSH      SSHConfig      `mapstructure:"ssh"`
	Check    CheckConfig    `mapstructure:"check"`
	UI       UIConfig       `mapstructure:"ui"`
	Export   ExportConfig   `mapstructure:"export"`
	Logging  LoggingConfig  `mapstructure:"logging"`
}

// AppConfig 应用程序配置
type AppConfig struct {
	Name     string `mapstructure:"name"`
	Version  string `mapstructure:"version"`
	Debug    bool   `mapstructure:"debug"`
	LogLevel string `mapstructure:"log_level"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type           string `mapstructure:"type"`
	Path           string `mapstructure:"path"`
	MaxConnections int    `mapstructure:"max_connections"`
	AutoMigrate    bool   `mapstructure:"auto_migrate"`
}

// SSHConfig SSH连接配置
type SSHConfig struct {
	Timeout        int `mapstructure:"timeout"`
	MaxConnections int `mapstructure:"max_connections"`
	RetryCount     int `mapstructure:"retry_count"`
	RetryInterval  int `mapstructure:"retry_interval"`
}

// CheckConfig 核查配置
type CheckConfig struct {
	ConcurrentHosts      int `mapstructure:"concurrent_hosts"`
	CommandTimeout       int `mapstructure:"command_timeout"`
	ResultRetentionDays  int `mapstructure:"result_retention_days"`
}

// UIConfig 界面配置
type UIConfig struct {
	Theme   string       `mapstructure:"theme"`
	Window  WindowConfig `mapstructure:"window"`
	Sidebar SidebarConfig `mapstructure:"sidebar"`
}

// WindowConfig 窗口配置
type WindowConfig struct {
	Width     int  `mapstructure:"width"`
	Height    int  `mapstructure:"height"`
	Resizable bool `mapstructure:"resizable"`
}

// SidebarConfig 侧边栏配置
type SidebarConfig struct {
	Width       int  `mapstructure:"width"`
	Collapsible bool `mapstructure:"collapsible"`
}

// ExportConfig 导出配置
type ExportConfig struct {
	OutputDir   string   `mapstructure:"output_dir"`
	Formats     []string `mapstructure:"formats"`
	TemplateDir string   `mapstructure:"template_dir"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string `mapstructure:"level"`
	File       string `mapstructure:"file"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

var globalConfig *Config

// Load 加载配置文件
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("读取配置文件失败: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 创建必要的目录
	if err := createDirectories(&config); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	globalConfig = &config
	return &config, nil
}

// Get 获取全局配置
func Get() *Config {
	return globalConfig
}

// setDefaults 设置默认配置值
func setDefaults() {
	viper.SetDefault("app.name", "Linux等保基线核查工具")
	viper.SetDefault("app.version", "1.0.0")
	viper.SetDefault("app.debug", false)
	viper.SetDefault("app.log_level", "info")

	viper.SetDefault("database.type", "sqlite")
	viper.SetDefault("database.path", "./data/security_checker.db")
	viper.SetDefault("database.max_connections", 10)
	viper.SetDefault("database.auto_migrate", true)

	viper.SetDefault("ssh.timeout", 30)
	viper.SetDefault("ssh.max_connections", 50)
	viper.SetDefault("ssh.retry_count", 3)
	viper.SetDefault("ssh.retry_interval", 5)

	viper.SetDefault("check.concurrent_hosts", 10)
	viper.SetDefault("check.command_timeout", 60)
	viper.SetDefault("check.result_retention_days", 30)

	viper.SetDefault("ui.theme", "auto")
	viper.SetDefault("ui.window.width", 1200)
	viper.SetDefault("ui.window.height", 800)
	viper.SetDefault("ui.window.resizable", true)
	viper.SetDefault("ui.sidebar.width", 200)
	viper.SetDefault("ui.sidebar.collapsible", true)

	viper.SetDefault("export.output_dir", "./exports")
	viper.SetDefault("export.formats", []string{"pdf", "excel", "json"})
	viper.SetDefault("export.template_dir", "./assets/templates")

	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.file", "./logs/app.log")
	viper.SetDefault("logging.max_size", 100)
	viper.SetDefault("logging.max_backups", 5)
	viper.SetDefault("logging.max_age", 30)
	viper.SetDefault("logging.compress", true)
}

// createDirectories 创建必要的目录
func createDirectories(config *Config) error {
	dirs := []string{
		filepath.Dir(config.Database.Path),
		config.Export.OutputDir,
		config.Export.TemplateDir,
		filepath.Dir(config.Logging.File),
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录 %s 失败: %w", dir, err)
		}
	}

	return nil
}
