# Linux等保基线核查工具开发需求文档

## 1. 项目概述

### 1.1 项目背景
随着网络安全等级保护制度的深入实施，企业和组织需要定期对其Linux系统进行等保基线核查，确保系统配置符合相关安全标准。当前市场缺乏易用、高效的Linux等保基线核查工具，本项目旨在开发一款功能完善的核查工具。

### 1.2 项目目标
- 开发一款基于Go语言的Linux等保基线核查工具
- 支持单主机和多主机批量核查
- 提供直观的GUI界面，降低使用门槛
- 支持核查结果导出，便于合规报告生成
- 确保工具的跨平台兼容性和高性能

### 1.3 项目范围
- 支持主流Linux发行版（CentOS、Ubuntu、RHEL、Debian等）
- 覆盖等保2.0标准的主要核查项目
- 提供单机版GUI应用程序
- 支持本地和远程主机核查

## 2. 功能需求分析

### 2.1 核心功能模块

#### 2.1.1 主机管理模块
- **单主机管理**
  - 添加/编辑/删除主机信息
  - 主机连接测试
  - 主机基本信息展示（IP、操作系统、版本等）
  
- **多主机组管理**
  - 创建/编辑/删除主机组
  - 批量导入主机信息
  - 主机组成员管理

#### 2.1.2 核查配置模块
- **核查项目配置**
  - 系统配置核查项
  - 安全策略核查项
  - 用户权限核查项
  - 网络配置核查项
  - 日志审计核查项
  - 文件权限核查项
  - 服务配置核查项

- **核查模板管理**
  - 预定义核查模板
  - 自定义核查模板
  - 模板导入/导出

#### 2.1.3 核查执行模块
- **核查任务管理**
  - 创建核查任务
  - 任务执行监控
  - 任务历史记录
  
- **实时核查**
  - 单主机实时核查
  - 多主机并发核查
  - 核查进度显示

#### 2.1.4 结果管理模块
- **结果展示**
  - 核查结果概览
  - 详细核查报告
  - 风险等级分类
  
- **结果导出**
  - PDF报告导出
  - Excel表格导出
  - JSON/XML数据导出

### 2.2 界面功能需求

#### 2.2.1 侧边栏导航
- 主机管理
- 主机组管理
- 核查配置
- 核查执行
- 结果查看
- 系统设置

#### 2.2.2 主要界面
- 仪表板（概览信息）
- 主机列表界面
- 核查配置界面
- 核查执行界面
- 结果展示界面

## 3. 技术架构设计

### 3.1 整体架构
```
┌─────────────────┐
│   GUI Layer     │  (Fyne v2)
├─────────────────┤
│  Business Logic │  (核查引擎、任务管理)
├─────────────────┤
│   Data Layer    │  (本地数据库、配置文件)
├─────────────────┤
│ Network Layer   │  (SSH连接、远程执行)
└─────────────────┘
```

### 3.2 核心组件

#### 3.2.1 GUI组件
- 使用Fyne v2框架
- 响应式布局设计
- 主题支持（明暗主题）

#### 3.2.2 核查引擎
- 模块化核查项设计
- 并发执行支持
- 结果聚合处理

#### 3.2.3 数据存储
- SQLite本地数据库
- JSON配置文件
- 加密敏感信息存储

#### 3.2.4 网络通信
- SSH协议支持
- 连接池管理
- 超时和重试机制

## 4. 数据模型设计

### 4.1 主要数据实体

#### 4.1.1 主机信息
```go
type Host struct {
    ID          int64     `json:"id"`
    Name        string    `json:"name"`
    IP          string    `json:"ip"`
    Port        int       `json:"port"`
    Username    string    `json:"username"`
    Password    string    `json:"password,omitempty"`
    PrivateKey  string    `json:"private_key,omitempty"`
    OS          string    `json:"os"`
    Version     string    `json:"version"`
    Status      string    `json:"status"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### 4.1.2 主机组
```go
type HostGroup struct {
    ID          int64     `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    HostIDs     []int64   `json:"host_ids"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

#### 4.1.3 核查任务
```go
type CheckTask struct {
    ID          int64     `json:"id"`
    Name        string    `json:"name"`
    TargetType  string    `json:"target_type"` // host/group
    TargetID    int64     `json:"target_id"`
    Template    string    `json:"template"`
    Status      string    `json:"status"`
    Progress    float64   `json:"progress"`
    StartTime   time.Time `json:"start_time"`
    EndTime     time.Time `json:"end_time"`
    CreatedAt   time.Time `json:"created_at"`
}
```

## 5. 界面设计规范

### 5.1 布局设计
- 采用侧边栏+主内容区域布局
- 侧边栏宽度：200px
- 主内容区域自适应
- 支持侧边栏折叠

### 5.2 色彩方案
- 主色调：#2196F3（蓝色）
- 辅助色：#FFC107（橙色）
- 成功色：#4CAF50（绿色）
- 警告色：#FF9800（橙色）
- 错误色：#F44336（红色）

### 5.3 组件规范
- 按钮：圆角4px，高度36px
- 输入框：圆角4px，高度32px
- 表格：斑马纹样式，行高40px
- 图标：统一使用Material Design图标

## 6. 开发计划

### 6.1 开发阶段

#### 第一阶段：基础框架搭建（2周）
- 项目初始化和依赖管理
- 基础GUI框架搭建
- 数据库设计和初始化
- 基础配置管理

#### 第二阶段：主机管理功能（2周）
- 单主机管理功能
- 主机组管理功能
- SSH连接功能
- 主机信息收集

#### 第三阶段：核查引擎开发（3周）
- 核查项目定义
- 核查引擎实现
- 并发执行机制
- 结果处理逻辑

#### 第四阶段：界面完善（2周）
- 核查配置界面
- 核查执行界面
- 结果展示界面
- 界面优化和美化

#### 第五阶段：导出和优化（1周）
- 结果导出功能
- 性能优化
- 错误处理完善
- 用户体验优化

### 6.2 里程碑
- M1：基础框架完成
- M2：主机管理功能完成
- M3：核查引擎完成
- M4：完整功能实现
- M5：产品发布就绪

## 7. 技术选型

### 7.1 开发语言和框架
- **开发语言**：Go 1.21+
- **GUI框架**：Fyne v2.4+
- **数据库**：SQLite 3
- **SSH库**：golang.org/x/crypto/ssh
- **配置管理**：Viper
- **日志库**：logrus

### 7.2 开发工具
- **IDE**：VS Code / GoLand
- **版本控制**：Git
- **构建工具**：Go Modules
- **打包工具**：fyne package

### 7.3 第三方依赖
```go
require (
    fyne.io/fyne/v2 v2.4.0
    github.com/spf13/viper v1.17.0
    github.com/sirupsen/logrus v1.9.3
    golang.org/x/crypto v0.14.0
    gorm.io/gorm v1.25.5
    gorm.io/driver/sqlite v1.5.4
)
```

## 8. 风险评估和应对策略

### 8.1 技术风险
- **风险**：SSH连接稳定性问题
- **应对**：实现连接池和重试机制

### 8.2 性能风险
- **风险**：大量主机并发核查性能问题
- **应对**：实现并发控制和资源限制

### 8.3 兼容性风险
- **风险**：不同Linux发行版兼容性问题
- **应对**：充分测试和适配主流发行版

## 9. 后续扩展计划

### 9.1 功能扩展
- Web版本开发
- 集中管理平台
- 自动化修复功能
- 合规报告模板

### 9.2 技术扩展
- 支持Windows系统核查
- 云平台集成
- API接口开放
- 插件机制支持
