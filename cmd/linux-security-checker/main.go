package main

import (
	"log"

	"linux-security-checker/internal/config"
	"linux-security-checker/internal/database"
	"linux-security-checker/pkg/logger"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志系统
	logConfig := logger.LogConfig{
		Level:      cfg.Logging.Level,
		File:       cfg.Logging.File,
		MaxSize:    cfg.Logging.MaxSize,
		MaxBackups: cfg.Logging.MaxBackups,
		MaxAge:     cfg.Logging.MaxAge,
		Compress:   cfg.Logging.Compress,
		Console:    true,
	}

	_, err = logger.Init(logConfig)
	if err != nil {
		log.Fatalf("初始化日志系统失败: %v", err)
	}

	logger.Info("启动Linux等保基线核查工具")

	// 初始化数据库
	dbConfig := database.Config{
		Type:           cfg.Database.Type,
		Path:           cfg.Database.Path,
		MaxConnections: cfg.Database.MaxConnections,
		AutoMigrate:    cfg.Database.AutoMigrate,
		LogLevel:       cfg.App.LogLevel,
	}

	if err := database.Init(dbConfig); err != nil {
		logger.Fatalf("初始化数据库失败: %v", err)
	}

	logger.Info("数据库初始化完成")

	// TODO: 在后续阶段添加GUI界面
	logger.Info("基础框架初始化完成")
	logger.Info("应用程序启动完成 - 当前为命令行模式")

	// 设置退出处理
	defer func() {
		logger.Info("正在关闭应用程序...")
		if err := database.Close(); err != nil {
			logger.Errorf("关闭数据库连接失败: %v", err)
		}
		logger.Info("应用程序已退出")
	}()

	// 暂时输出成功信息
	logger.Info("Linux等保基线核查工具基础框架已成功启动！")
	logger.Info("配置文件: %s", cfg.Database.Path)
	logger.Info("日志文件: %s", cfg.Logging.File)
}
