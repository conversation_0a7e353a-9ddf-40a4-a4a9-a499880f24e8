package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger 日志管理器
type Logger struct {
	*logrus.Logger
	config LogConfig
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string
	File       string
	MaxSize    int  // MB
	MaxBackups int
	MaxAge     int  // days
	Compress   bool
	Console    bool
}

var globalLogger *Logger

// Init 初始化日志系统
func Init(config LogConfig) (*Logger, error) {
	logger := &Logger{
		Logger: logrus.New(),
		config: config,
	}

	// 设置日志级别
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
		ForceColors:     false,
	})

	// 配置输出
	var writers []io.Writer

	// 控制台输出
	if config.Console {
		writers = append(writers, os.Stdout)
	}

	// 文件输出
	if config.File != "" {
		// 确保日志目录存在
		if err := os.MkdirAll(filepath.Dir(config.File), 0755); err != nil {
			return nil, fmt.Errorf("创建日志目录失败: %w", err)
		}

		// 配置日志轮转
		fileWriter := &lumberjack.Logger{
			Filename:   config.File,
			MaxSize:    config.MaxSize,
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge,
			Compress:   config.Compress,
		}
		writers = append(writers, fileWriter)
	}

	if len(writers) > 0 {
		logger.SetOutput(io.MultiWriter(writers...))
	}

	globalLogger = logger
	return logger, nil
}

// Get 获取全局日志实例
func Get() *Logger {
	if globalLogger == nil {
		// 如果没有初始化，使用默认配置
		config := LogConfig{
			Level:   "info",
			Console: true,
		}
		Init(config)
	}
	return globalLogger
}

// WithField 添加字段
func (l *Logger) WithField(key string, value interface{}) *logrus.Entry {
	return l.Logger.WithField(key, value)
}

// WithFields 添加多个字段
func (l *Logger) WithFields(fields logrus.Fields) *logrus.Entry {
	return l.Logger.WithFields(fields)
}

// WithError 添加错误字段
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// Debug 调试日志
func (l *Logger) Debug(args ...interface{}) {
	l.Logger.Debug(args...)
}

// Debugf 格式化调试日志
func (l *Logger) Debugf(format string, args ...interface{}) {
	l.Logger.Debugf(format, args...)
}

// Info 信息日志
func (l *Logger) Info(args ...interface{}) {
	l.Logger.Info(args...)
}

// Infof 格式化信息日志
func (l *Logger) Infof(format string, args ...interface{}) {
	l.Logger.Infof(format, args...)
}

// Warn 警告日志
func (l *Logger) Warn(args ...interface{}) {
	l.Logger.Warn(args...)
}

// Warnf 格式化警告日志
func (l *Logger) Warnf(format string, args ...interface{}) {
	l.Logger.Warnf(format, args...)
}

// Error 错误日志
func (l *Logger) Error(args ...interface{}) {
	l.Logger.Error(args...)
}

// Errorf 格式化错误日志
func (l *Logger) Errorf(format string, args ...interface{}) {
	l.Logger.Errorf(format, args...)
}

// Fatal 致命错误日志
func (l *Logger) Fatal(args ...interface{}) {
	l.Logger.Fatal(args...)
}

// Fatalf 格式化致命错误日志
func (l *Logger) Fatalf(format string, args ...interface{}) {
	l.Logger.Fatalf(format, args...)
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level string) error {
	logLevel, err := logrus.ParseLevel(strings.ToLower(level))
	if err != nil {
		return fmt.Errorf("无效的日志级别: %s", level)
	}
	l.Logger.SetLevel(logLevel)
	return nil
}

// GetLevel 获取当前日志级别
func (l *Logger) GetLevel() string {
	return l.Logger.GetLevel().String()
}

// 全局便捷函数
func Debug(args ...interface{}) {
	Get().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	Get().Debugf(format, args...)
}

func Info(args ...interface{}) {
	Get().Info(args...)
}

func Infof(format string, args ...interface{}) {
	Get().Infof(format, args...)
}

func Warn(args ...interface{}) {
	Get().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	Get().Warnf(format, args...)
}

func Error(args ...interface{}) {
	Get().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	Get().Errorf(format, args...)
}

func Fatal(args ...interface{}) {
	Get().Fatal(args...)
}

func Fatalf(format string, args ...interface{}) {
	Get().Fatalf(format, args...)
}

func WithField(key string, value interface{}) *logrus.Entry {
	return Get().WithField(key, value)
}

func WithFields(fields logrus.Fields) *logrus.Entry {
	return Get().WithFields(fields)
}

func WithError(err error) *logrus.Entry {
	return Get().WithError(err)
}
