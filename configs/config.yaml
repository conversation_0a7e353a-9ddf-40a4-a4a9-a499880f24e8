# Linux等保基线核查工具配置文件

# 应用程序配置
app:
  name: "Linux等保基线核查工具"
  version: "1.0.0"
  debug: false
  log_level: "info"

# 数据库配置
database:
  type: "sqlite"
  path: "./data/security_checker.db"
  max_connections: 10
  auto_migrate: true

# SSH连接配置
ssh:
  timeout: 30
  max_connections: 50
  retry_count: 3
  retry_interval: 5

# 核查配置
check:
  concurrent_hosts: 10
  command_timeout: 60
  result_retention_days: 30

# 界面配置
ui:
  theme: "auto"  # auto, light, dark
  window:
    width: 1200
    height: 800
    resizable: true
  sidebar:
    width: 200
    collapsible: true

# 导出配置
export:
  output_dir: "./exports"
  formats:
    - "pdf"
    - "excel"
    - "json"
  template_dir: "./assets/templates"

# 日志配置
logging:
  level: "info"
  file: "./logs/app.log"
  max_size: 100  # MB
  max_backups: 5
  max_age: 30    # days
  compress: true
